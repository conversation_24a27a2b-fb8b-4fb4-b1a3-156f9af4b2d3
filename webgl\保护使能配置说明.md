# 保护使能配置页面说明

## 概述

保护使能配置页面是桂林智源SVG数字化系统的重要组成部分，用于配置和管理64个保护功能使能参数。该页面采用工业监控界面的深色科技主题风格，提供实时数据监控和参数配置功能。

## 文件结构

```
webgl/
├── 保护使能.html              # 主配置页面
├── 保护使能测试.html          # 测试页面
└── 保护使能配置说明.md        # 本说明文档

docs/
└── 物模型-保护使能.json       # 参数定义文件（64个参数）
```

## 功能特性

### 1. 界面布局
- **页面尺寸**: 1920×1080像素，适配大屏显示
- **双面板设计**: 左右两个面板分别显示32个参数
- **实时状态栏**: 显示MQTT连接状态、数据接收情况、修改计数等
- **科技感设计**: 深色主题，蓝色高亮，动态背景效果

### 2. 参数管理
- **64个保护使能参数**: 涵盖电网保护、设备保护、通信保护等
- **实时数据显示**: 显示参数的当前值
- **交互式配置**: 通过开关控件修改参数设定值
- **修改跟踪**: 实时统计已修改的参数数量

### 3. MQTT通信
- **连接配置**:
  - clientId: `S&D19EOEN59V1M&J97&19`
  - username: `bydq_admin`
  - password: `P5OGL9C1V3E7W06P`
  - port: `1883`
- **订阅主题**: `/J97/D19EOEN59V1M/function/get`
- **发布主题**: `/J97/D19EOEN59V1M/property/post`
- **多重连接**: 支持多种连接方式，包括本地和远程服务器
- **模拟模式**: 连接失败时自动启用数据模拟

### 4. 数据处理
- **实时更新**: 订阅MQTT消息，实时更新参数当前值
- **批量配置**: 支持一次性下载多个修改的参数
- **错误处理**: 完善的错误处理和状态提示
- **数据验证**: 确保参数值的有效性

## 使用方法

### 1. 直接访问
```
http://localhost/webgl/保护使能.html
```

### 2. iframe嵌入
```html
<iframe src="保护使能.html" width="1920" height="1080"></iframe>
```

### 3. 测试环境
```
http://localhost/webgl/保护使能测试.html
```

## 操作流程

### 1. 连接MQTT服务器
1. 点击"连接MQTT"按钮
2. 系统会自动尝试多种连接方式
3. 连接成功后状态指示器变为绿色
4. 如果连接失败，会自动启用模拟模式

### 2. 查看实时数据
1. 连接成功后，系统会自动订阅数据主题
2. 参数的"当前值"列会实时更新
3. 状态栏显示数据接收统计

### 3. 修改参数配置
1. 点击参数行中的开关控件
2. 开关状态会立即更新（绿色=使能，红色=禁用）
3. "设定值"列显示修改后的值
4. 状态栏的"已修改"计数会增加

### 4. 下载配置到设备
1. 修改完参数后，点击"下载配置"按钮
2. 系统会将所有修改的参数打包成MQTT消息
3. 通过发布主题发送到设备
4. 成功后会显示确认消息并清空修改记录

## 参数说明

### 保护功能使能1（参数1-32）
包含电网保护、SVG器件保护、通信保护等基础保护功能：
- 电网导纳估计保护（1-8）
- 电网电压和信号保护（9-11）
- SVG器件故障保护（12-14）
- 电流保护（15-21）
- 功率单元保护（22-27）
- 通信保护（28-32）

### 保护功能使能2（参数33-64）
包含设备故障保护、系统保护、水冷保护等高级保护功能：
- 设备故障保护（33-44）
- 变压器保护（45-51）
- 系统综合保护（52-64）

## 技术实现

### 1. 前端技术
- **HTML5 + CSS3**: 响应式布局和动画效果
- **JavaScript ES6**: 模块化编程和异步处理
- **MQTT.js**: WebSocket MQTT客户端库
- **Font Awesome**: 图标库

### 2. 数据格式
```json
{
  "id": "timestamp",
  "version": "1.0",
  "params": {
    "PROT_ENABLE_1": {
      "value": 1,
      "time": 1640995200000
    }
  }
}
```

### 3. 状态管理
- 使用类封装管理器模式
- 分离数据处理和界面更新逻辑
- 支持多种数据源（MQTT、模拟、测试）

## 测试功能

### 1. 测试页面功能
- **iframe嵌入**: 在测试环境中嵌入配置页面
- **数据模拟**: 生成随机测试数据
- **实时监控**: 监控配置页面的状态变化
- **日志记录**: 详细的操作日志

### 2. 测试操作
1. 打开测试页面
2. 点击"发送测试数据"发送单次数据
3. 点击"模拟MQTT数据"启动持续数据流
4. 观察配置页面的实时更新

## 故障排除

### 1. MQTT连接问题
- 检查网络连接
- 确认MQTT服务器地址和端口
- 验证用户名和密码
- 查看浏览器控制台错误信息

### 2. 数据不更新
- 确认MQTT连接状态
- 检查订阅主题是否正确
- 验证数据格式是否符合要求
- 查看控制台日志

### 3. 配置下载失败
- 确认MQTT连接正常
- 检查发布主题权限
- 验证消息格式
- 查看设备端接收情况

## 扩展说明

### 1. 添加新参数
1. 修改`docs/物模型-保护使能.json`文件
2. 更新页面中的参数定义
3. 调整界面布局（如需要）

### 2. 自定义样式
- 修改CSS变量调整颜色主题
- 调整网格布局适配不同屏幕
- 添加动画效果增强用户体验

### 3. 集成到主系统
- 通过iframe方式嵌入主界面
- 使用postMessage进行跨框架通信
- 统一状态管理和错误处理

## 版本信息

- **版本**: 1.0.0
- **创建日期**: 2025-01-08
- **适用系统**: 桂林智源SVG数字化系统
- **兼容性**: 现代浏览器（Chrome 80+, Firefox 75+, Safari 13+）
