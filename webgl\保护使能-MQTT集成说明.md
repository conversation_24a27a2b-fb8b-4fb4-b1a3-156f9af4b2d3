# 保护使能页面 MQTT 实时数据集成说明

## 概述

已成功为 `保护使能.html` 页面集成 MQTT 实时数据功能，实现与电气系统相同的 MQTT 服务器连接和数据订阅机制。

## 集成功能

### 1. MQTT 连接配置

- **服务器地址**: `wss://mqtt.qizhiyun.cc/mqtt`
- **认证方式**: 使用与电气系统相同的用户名和 JWT Token
  - 用户名: `FastBee`
  - JWT Token: 与 main.html 中相同的认证令牌
- **订阅主题**: `/197/D19EOEN59V1MJ/ws/service`
- **连接参数**:
  - QoS: 1
  - Keep Alive: 30秒
  - Clean Session: true
  - 连接超时: 60秒

### 2. 数据结构映射

根据 `docs/物模型-保护使能.csv` 文件中定义的数据结构，实现了完整的 64 个保护使能参数映射：

#### 第一组参数 (1-32)

- `2101_GOA1`: 电网线电压有效值Ⅰ段过压报警使能
- `2101_GOP2`: 电网线电压有效值Ⅱ段过压保护使能
- `2101_GOP3`: 电网线电压有效值Ⅲ段过压保护使能
- ... (共32个参数)

#### 第二组参数 (33-64)

- `2102_DF`: DRAM故障使能
- `2102_DWPF`: DRAM写参数故障使能
- `2102_WWRP`: WDI看门狗复位保护使能
- ... (共32个参数)

### 3. 实时数据更新

- **自动更新**: 接收到 MQTT 消息时自动更新对应参数的当前值
- **状态指示**: 页面右上角显示 MQTT 连接状态和数据更新时间
- **错误处理**: 连接失败时自动重连，最多重试5次
- **数据验证**: 验证接收到的数据格式和参数映射

### 4. 界面增强

- **连接状态指示器**: 实时显示 MQTT 连接状态（已连接/未连接）
- **数据时间戳**: 显示最后一次数据更新时间
- **视觉反馈**: 连接状态用颜色区分（绿色=已连接，红色=未连接）

## 核心类和方法

### MQTTProtectionManager 类

负责 MQTT 连接和数据处理的核心管理器：

```javascript
class MQTTProtectionManager {
    constructor()           // 初始化连接参数
    async init()           // 建立 MQTT 连接
    async connectMQTT()    // 连接到 MQTT 服务器
    async subscribeToTopic() // 订阅数据主题
    setupMessageHandler()  // 设置消息处理器
    processProtectionData() // 处理保护使能数据
    scheduleReconnect()    // 自动重连机制
    getConnectionStatus()  // 获取连接状态
}
```

### ProtectionEnableManager 类增强

原有的保护管理器类增加了 MQTT 数据处理功能：

```javascript
initializeMQTTMapping()        // 初始化参数映射表
updateCurrentValueFromMQTT()   // 从 MQTT 数据更新单个参数
updateFromMQTTData()          // 批量更新 MQTT 数据
```

## 测试和调试

### 测试页面

创建了 `test-protection-mqtt.html` 测试页面，提供：

- MQTT 连接测试
- 数据接收验证
- 参数映射测试
- 实时日志显示

### 调试功能

在浏览器控制台中可使用以下调试命令：

```javascript
// 查看连接状态
mqttProtectionManager.getConnectionStatus()

// 手动重连
mqttProtectionManager.reconnect()

// 查看参数映射表
protectionManager.mqttParameterMapping
```

## 数据流程

1. **连接建立**: 页面加载时自动连接到 MQTT 服务器
2. **主题订阅**: 成功连接后订阅保护使能数据主题
3. **数据接收**: 接收到 MQTT 消息时解析 JSON 数据
4. **参数映射**: 根据标识符映射到对应的保护使能参数
5. **界面更新**: 更新参数当前值并刷新显示
6. **状态维护**: 更新连接状态和数据时间戳

## 错误处理和恢复

- **连接失败**: 自动重连机制，使用指数退避算法
- **数据解析错误**: 记录错误日志，不影响其他数据处理
- **参数映射失败**: 记录未知参数，继续处理其他参数
- **网络中断**: 自动检测并尝试重新连接

## 兼容性说明

- 与现有电气系统 MQTT 配置完全兼容
- 使用相同的认证机制和服务器地址
- 不影响原有的手动配置功能
- 支持实时数据和手动设置的混合模式

## 部署注意事项

1. 确保网络能访问 `mqtt.qizhiyun.cc` 服务器
2. MQTT 客户端库通过 CDN 加载，需要网络连接
3. JWT Token 有效期需要定期更新
4. 建议在生产环境中监控连接状态和数据接收情况

## 后续扩展

- 可以添加数据历史记录功能
- 支持参数设置值的 MQTT 下发
- 增加数据质量监控和统计
- 实现参数变化的实时告警
