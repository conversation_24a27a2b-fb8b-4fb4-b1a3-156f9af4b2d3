@echo off
chcp 65001 >nul
echo ========================================
echo 桂林智源SVG数字化系统
echo 保护使能配置页面测试启动器
echo ========================================
echo.

echo 正在启动本地HTTP服务器...
echo.

REM 检查是否安装了Python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo 使用Python启动HTTP服务器...
    echo 访问地址: http://localhost:8000/保护使能测试.html
    echo.
    echo 按Ctrl+C停止服务器
    echo.
    python -m http.server 8000
) else (
    REM 检查是否安装了Node.js
    node --version >nul 2>&1
    if %errorlevel% == 0 (
        echo 使用Node.js启动HTTP服务器...
        echo 访问地址: http://localhost:8000/保护使能测试.html
        echo.
        echo 按Ctrl+C停止服务器
        echo.
        npx http-server -p 8000
    ) else (
        echo 错误: 未找到Python或Node.js
        echo 请安装Python 3.x 或 Node.js 来运行HTTP服务器
        echo.
        echo 或者手动将文件部署到Web服务器中访问
        echo.
        pause
    )
)
