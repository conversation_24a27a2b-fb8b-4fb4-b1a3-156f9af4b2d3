{"properties": [{"id": "PROT_ENABLE_1", "name": "电网导纳估计有效值I段过", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_2", "name": "电网导纳估计有效值II段", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_3", "name": "电网导纳估计有效值III段过", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_4", "name": "电网导纳估计有效值过", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_5", "name": "电网导纳估计有效值I段欠", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_6", "name": "电网导纳估计有效值II段欠", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_7", "name": "电网导纳估计有效值III段欠", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_8", "name": "电网导纳估计有效值不平衡", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_9", "name": "电网导纳估计球形保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_10", "name": "电网电压失压保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_11", "name": "两步信号失去保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_12", "name": "SVG器件故障保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_13", "name": "保护板失去通讯自检故障", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_14", "name": "零序电压监测保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_15", "name": "SVG输出电流有效值I段", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_16", "name": "SVG输出电流有效值II段", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_17", "name": "SVG输出电流有效值III段", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_18", "name": "SVG输出电流保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_19", "name": "SVG输出电流保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_20", "name": "SVG输出电流保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_21", "name": "SVG器件电流过流", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_22", "name": "PT故障保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_23", "name": "功率单元UDC过压保护", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_24", "name": "功率单元UDC欠压保护", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_25", "name": "功率单元体温过高", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_26", "name": "功率单元UDC不平衡保护", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_27", "name": "功率单元体温过高", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_28", "name": "RS485通信超时故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_29", "name": "功率单元体温一致", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_30", "name": "RS485通信超时故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_31", "name": "DRAM故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_32", "name": "DRAM参数故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_33", "name": "DRAM故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_34", "name": "DRAM参数故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_35", "name": "WDI看门狗复位保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_36", "name": "充电超时故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_37", "name": "过载故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_38", "name": "充电接触器不吸合使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_39", "name": "断路器QF不闭合使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_40", "name": "断路器QF不分开使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_41", "name": "断路器QF不分开使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_42", "name": "CAN通信发送超时使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_43", "name": "读铁电参数错误使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_44", "name": "水冷系统电源故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_45", "name": "读铁电参数错误使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_46", "name": "变压器温度报警使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_47", "name": "变压器温度跳闸使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_48", "name": "变压器轻瓦斯报警使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_49", "name": "变压器重瓦斯跳闸使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_50", "name": "变压器超温跳闸使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_51", "name": "变压器力矩保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_52", "name": "风机故障保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_53", "name": "单元故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_54", "name": "阀门过流2%保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_55", "name": "电网低电压1%保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_56", "name": "电网低电压2%保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_57", "name": "自动检查全数据故障", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_58", "name": "故障复位超时保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_59", "name": "拒动光纤断线保护使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_60", "name": "水冷系统低流量警告使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_61", "name": "水冷系统综合故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_62", "name": "水冷系统请求停止使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_63", "name": "高温光纤通信故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}, {"id": "PROT_ENABLE_64", "name": "水冷系统故障使能", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 0, "datatype": {"type": "bool", "specs": {"0": "禁用", "1": "使能"}}}], "functions": [], "events": []}