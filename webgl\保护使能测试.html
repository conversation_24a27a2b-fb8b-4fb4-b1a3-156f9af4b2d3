<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保护使能配置测试 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="logo.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 100%);
            color: #ffffff;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(26, 31, 46, 0.9);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 30px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid rgba(0, 212, 255, 0.3);
        }

        .header h1 {
            font-size: 32px;
            color: #00d4ff;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .test-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: 2px solid rgba(0, 123, 255, 0.5);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
            border: 2px solid rgba(40, 167, 69, 0.5);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: black;
            border: 2px solid rgba(255, 193, 7, 0.5);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
        }

        .status-panel {
            background: rgba(42, 49, 66, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .status-item {
            text-align: center;
        }

        .status-label {
            font-size: 14px;
            color: #b8c5d6;
            margin-bottom: 5px;
        }

        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #00d4ff;
        }

        .iframe-container {
            width: 100%;
            height: 800px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            overflow: hidden;
        }

        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .log-panel {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 20px;
        }

        .connected { color: #28a745; }
        .disconnected { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>保护使能配置测试</h1>
            <p>SVG数字化系统保护功能使能配置界面测试环境</p>
        </div>

        <div class="test-controls">
            <button class="btn btn-primary" onclick="openProtectionConfig()">打开保护使能配置</button>
            <button class="btn btn-success" onclick="sendTestData()">发送测试数据</button>
            <button class="btn btn-warning" onclick="simulateMQTTData()">模拟MQTT数据</button>
            <button class="btn btn-primary" onclick="clearLog()">清空日志</button>
        </div>

        <div class="status-panel">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">系统状态</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">测试状态</div>
                    <div class="status-value" id="test-status">就绪</div>
                </div>
                <div class="status-item">
                    <div class="status-label">数据发送</div>
                    <div class="status-value" id="data-sent">0</div>
                </div>
                <div class="status-item">
                    <div class="status-label">模拟参数</div>
                    <div class="status-value" id="param-count">64</div>
                </div>
                <div class="status-item">
                    <div class="status-label">最后操作</div>
                    <div class="status-value" id="last-action">--</div>
                </div>
            </div>
        </div>

        <div class="iframe-container">
            <iframe id="config-iframe" src="保护使能.html"></iframe>
        </div>

        <div class="log-panel" id="log-panel">
            <div>保护使能配置测试环境初始化完成...</div>
        </div>
    </div>

    <script>
        let dataSentCount = 0;
        let testInterval = null;

        /**
         * 打开保护使能配置页面
         */
        function openProtectionConfig() {
            const iframe = document.getElementById('config-iframe');
            iframe.src = '保护使能.html?' + Date.now(); // 添加时间戳强制刷新
            log('重新加载保护使能配置页面');
            updateStatus('test-status', '配置页面已加载');
            updateStatus('last-action', '打开配置');
        }

        /**
         * 发送测试数据
         */
        function sendTestData() {
            const testData = {
                id: Date.now().toString(),
                version: "1.0",
                properties: {}
            };

            // 生成64个保护使能参数的随机测试数据
            for (let i = 1; i <= 64; i++) {
                const paramId = `PROT_ENABLE_${i}`;
                testData.properties[paramId] = {
                    value: Math.random() > 0.5 ? 1 : 0,
                    time: Date.now()
                };
            }

            dataSentCount++;
            log(`发送测试数据 #${dataSentCount}: ${JSON.stringify(testData).substring(0, 100)}...`);
            
            // 通过postMessage向iframe发送数据
            const iframe = document.getElementById('config-iframe');
            if (iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'mqtt-data',
                    data: testData
                }, '*');
            }

            updateStatus('data-sent', dataSentCount);
            updateStatus('test-status', '数据已发送');
            updateStatus('last-action', '发送测试数据');
        }

        /**
         * 模拟MQTT数据流
         */
        function simulateMQTTData() {
            if (testInterval) {
                clearInterval(testInterval);
                testInterval = null;
                log('停止MQTT数据模拟');
                updateStatus('test-status', '模拟已停止');
                updateStatus('last-action', '停止模拟');
                return;
            }

            log('开始模拟MQTT数据流...');
            updateStatus('test-status', '模拟运行中');
            updateStatus('last-action', '开始模拟');

            testInterval = setInterval(() => {
                sendTestData();
            }, 3000); // 每3秒发送一次数据
        }

        /**
         * 更新状态显示
         */
        function updateStatus(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
            }
        }

        /**
         * 清空日志
         */
        function clearLog() {
            document.getElementById('log-panel').innerHTML = '';
            log('日志已清空');
        }

        /**
         * 记录日志
         */
        function log(message, type = 'info') {
            const logPanel = document.getElementById('log-panel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            
            let color = '#00ff00';
            if (type === 'error') color = '#ff4444';
            if (type === 'warning') color = '#ffaa00';
            
            logEntry.style.color = color;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            if (event.data.type === 'config-update') {
                log(`配置更新: ${event.data.message}`);
            } else if (event.data.type === 'mqtt-status') {
                log(`MQTT状态: ${event.data.status}`);
                updateStatus('test-status', event.data.status);
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('保护使能配置测试环境初始化完成');
            updateStatus('last-action', '系统初始化');
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (testInterval) {
                clearInterval(testInterval);
            }
        });
    </script>
</body>
</html>
