绘制其他页面：
- [调试参数1](https://exdraw.qizhiyun.cc/webgl3d/%E8%B0%83%E8%AF%95%E5%8F%82%E6%95%B01.html)：
  - 设备操作：特殊
  - 控制模式
  - 系统参数
  - 保护使能
  - 保护参数
  - 滤波控制
  - 单元信息：特殊
- [调试参数2](https://exdraw.qizhiyun.cc/webgl3d/%E8%B0%83%E8%AF%95%E5%8F%82%E6%95%B02.html)
  - 设备操作：特殊
  - 控制模式
  - 控制参数 1
  - 控制参数 2
  - 系统参数
  - 谐波控制
  - 录波参数：特殊，暂时不做
  - 旁路控制：特殊

1. 参考组态截图绘制，提示语如下：

目标页面名称：设备操作

创建一个配置页面，具体要求如下：

**页面布局和内容：**
1. 参考提供的截图设计页面布局，显示64个保护使能参数
2. 每个参数的配置信息从 `docs/物模型-保护使能.json` 文件中获取
3. 参数的「当前值」通过订阅MQTT消息实时获取和更新
4. 每个参数应包含：参数名称、当前值显示、设定值输入控件
5. 页面应采用工业监控界面的深色科技主题风格，与现有系统保持一致


**功能实现：**
1. 页面加载时建立MQTT连接并订阅消息
2. 实时解析订阅消息，更新各参数的「当前值」显示
3. 提供输入控件让用户修改各参数的「设定值」
4. 在页面底部添加"下载"按钮
5. 点击按钮时，收集所有已修改的「设定值」参数
6. 将修改的参数组装成符合协议格式的MQTT消息
7. 通过发布主题发送配置消息到设备

**技术要求：**
- 页面尺寸：1920×1080像素（与现有系统页面保持一致）
- 使用iframe弹窗方式显示（如果从主界面调用）
- 确保MQTT消息格式与 `物模型-保护使能.json` 中定义的数据结构匹配
- 添加适当的错误处理和连接状态指示

