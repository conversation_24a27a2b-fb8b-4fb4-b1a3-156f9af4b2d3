<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保护使能配置 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="logo.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 100%);
            color: #ffffff;
            width: 1920px;
            height: 1080px;
            overflow: hidden;
            position: relative;
        }

        /* 科技感背景动画 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px 0;
            border-bottom: 2px solid rgba(0, 212, 255, 0.3);
        }

        .header h1 {
            font-size: 36px;
            color: #00d4ff;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .header .subtitle {
            font-size: 18px;
            color: #b8c5d6;
            opacity: 0.8;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
            animation: pulse 2s infinite;
        }

        .status-indicator.connected {
            background: #28a745;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .main-content {
            flex: 1;
            display: flex;
            gap: 20px;
            overflow: hidden;
        }

        .protection-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.9);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        .panel-title {
            font-size: 24px;
            color: #00d4ff;
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.3);
        }

        .params-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .param-row {
            display: grid;
            grid-template-columns: 40px 1fr 80px 80px 80px;
            gap: 10px;
            align-items: center;
            padding: 8px 12px;
            background: rgba(42, 49, 66, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .param-row:hover {
            border-color: #00d4ff;
            background: rgba(42, 49, 66, 0.8);
        }

        .param-index {
            font-size: 14px;
            color: #7a8ba0;
            text-align: center;
            font-weight: bold;
        }

        .param-name {
            font-size: 14px;
            color: #ffffff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .param-current {
            text-align: center;
            font-size: 14px;
            color: #00d4ff;
            font-weight: bold;
        }

        .param-setting {
            text-align: center;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #dc3545;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid rgba(0, 212, 255, 0.3);
        }

        .toggle-switch.active {
            background: #28a745;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .toggle-switch.active::before {
            transform: translateX(26px);
        }

        .param-value {
            text-align: center;
            font-size: 14px;
            color: #ffffff;
        }

        .control-panel {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 1000;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: 2px solid rgba(0, 123, 255, 0.5);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
            border: 2px solid rgba(40, 167, 69, 0.5);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        /* 滚动条样式 */
        .protection-panel::-webkit-scrollbar {
            width: 8px;
        }

        .protection-panel::-webkit-scrollbar-track {
            background: rgba(26, 31, 46, 0.5);
            border-radius: 4px;
        }

        .protection-panel::-webkit-scrollbar-thumb {
            background: rgba(0, 212, 255, 0.5);
            border-radius: 4px;
        }

        .protection-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 212, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> 保护使能</h1>
            <div class="subtitle">SVG数字化系统保护功能使能配置界面</div>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator" id="mqtt-status"></div>
                <span>MQTT连接状态: <span id="connection-text">未连接</span></span>
            </div>
            <div class="status-item">
                <i class="fas fa-clock"></i>
                <span>最后更新: <span id="last-update">--</span></span>
            </div>
            <div class="status-item">
                <i class="fas fa-database"></i>
                <span>数据接收: <span id="data-count">0</span> 条</span>
            </div>
            <div class="status-item">
                <i class="fas fa-edit"></i>
                <span>已修改: <span id="modified-count">0</span> 项</span>
            </div>
        </div>

        <div class="main-content">
            <div class="protection-panel">
                <div class="panel-title">保护功能使能1</div>
                <div class="params-grid" id="protection-grid-1">
                    <!-- 保护使能参数1-32将在这里动态生成 -->
                </div>
            </div>

            <div class="protection-panel">
                <div class="panel-title">保护功能使能2</div>
                <div class="params-grid" id="protection-grid-2">
                    <!-- 保护使能参数33-64将在这里动态生成 -->
                </div>
            </div>
        </div>

        <div class="control-panel">
            <button class="btn btn-primary" onclick="connectMQTT()">
                <i class="fas fa-plug"></i> 连接MQTT
            </button>
            <button class="btn btn-success" onclick="downloadConfig()">
                <i class="fas fa-download"></i> 下载配置
            </button>
        </div>
    </div>

    <!-- 引入MQTT库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <script>
        /**
         * 保护使能配置管理类
         * 负责MQTT通信、数据处理和界面更新
         */
        class ProtectionEnableManager {
            constructor() {
                this.mqttClient = null;
                this.isConnected = false;
                this.dataCount = 0;
                this.modifiedCount = 0;
                this.protectionParams = [];
                this.currentValues = {};
                this.modifiedValues = {};

                // MQTT配置
                this.mqttConfig = {
                    clientId: 'S&D19EOEN59V1M&J97&19',
                    username: 'bydq_admin',
                    password: 'P5OGL9C1V3E7W06P',
                    port: 1883,
                    subscribeTopic: '/J97/D19EOEN59V1M/function/get',
                    publishTopic: '/J97/D19EOEN59V1M/property/post'
                };

                this.initializeParams();
                this.createParamGrids();
                this.updateStatusDisplay();
            }

            /**
             * 初始化保护使能参数定义
             * 根据截图中的参数创建64个保护使能参数
             */
            initializeParams() {
                // 第一组32个参数（保护功能使能1）
                const group1Params = [
                    "电网导纳估计有效值I段过", "电网导纳估计有效值II段", "电网导纳估计有效值III段过", "电网导纳估计有效值过",
                    "电网导纳估计有效值I段欠", "电网导纳估计有效值II段欠", "电网导纳估计有效值III段欠", "电网导纳估计有效值不平衡",
                    "电网导纳估计球形保护使能", "电网电压失压保护使能", "两步信号失去保护使能", "SVG器件故障保护使能",
                    "保护板失去通讯自检故障", "零序电压监测保护使能", "SVG输出电流有效值I段", "SVG输出电流有效值II段",
                    "SVG输出电流有效值III段", "SVG输出电流保护使能", "SVG输出电流保护使能", "SVG输出电流保护使能",
                    "SVG器件电流过流", "PT故障保护使能", "功率单元UDC过压保护", "功率单元UDC欠压保护",
                    "功率单元体温过高", "功率单元UDC不平衡保护", "功率单元体温过高", "RS485通信超时故障使能",
                    "功率单元体温一致", "RS485通信超时故障使能", "DRAM故障使能", "DRAM参数故障使能"
                ];

                // 第二组32个参数（保护功能使能2）
                const group2Params = [
                    "DRAM故障使能", "DRAM参数故障使能", "WDI看门狗复位保护使能", "充电超时故障使能",
                    "过载故障使能", "充电接触器不吸合使能", "断路器QF不闭合使能", "断路器QF不分开使能",
                    "断路器QF不分开使能", "CAN通信发送超时使能", "读铁电参数错误使能", "水冷系统电源故障使能",
                    "读铁电参数错误使能", "变压器温度报警使能", "变压器温度跳闸使能", "变压器轻瓦斯报警使能",
                    "变压器重瓦斯跳闸使能", "变压器超温跳闸使能", "变压器力矩保护使能", "风机故障保护使能",
                    "单元故障使能", "阀门过流2%保护使能", "电网低电压1%保护使能", "电网低电压2%保护使能",
                    "自动检查全数据故障", "故障复位超时保护使能", "拒动光纤断线保护使能", "水冷系统低流量警告使能",
                    "水冷系统综合故障使能", "水冷系统请求停止使能", "高温光纤通信故障使能", "水冷系统故障使能"
                ];

                // 创建完整的64个参数
                for (let i = 0; i < 32; i++) {
                    this.protectionParams.push({
                        id: `PROT_ENABLE_${i + 1}`,
                        index: i + 1,
                        name: group1Params[i] || `保护使能参数${i + 1}`,
                        currentValue: 0,
                        settingValue: 0,
                        group: 1
                    });
                }

                for (let i = 0; i < 32; i++) {
                    this.protectionParams.push({
                        id: `PROT_ENABLE_${i + 33}`,
                        index: i + 33,
                        name: group2Params[i] || `保护使能参数${i + 33}`,
                        currentValue: 0,
                        settingValue: 0,
                        group: 2
                    });
                }
            }

            /**
             * 创建参数网格界面
             */
            createParamGrids() {
                const grid1 = document.getElementById('protection-grid-1');
                const grid2 = document.getElementById('protection-grid-2');

                // 创建第一组参数（1-32）
                this.protectionParams.slice(0, 32).forEach(param => {
                    const paramRow = this.createParamRow(param);
                    grid1.appendChild(paramRow);
                });

                // 创建第二组参数（33-64）
                this.protectionParams.slice(32, 64).forEach(param => {
                    const paramRow = this.createParamRow(param);
                    grid2.appendChild(paramRow);
                });
            }

            /**
             * 创建单个参数行
             */
            createParamRow(param) {
                const row = document.createElement('div');
                row.className = 'param-row';
                row.innerHTML = `
                    <div class="param-index">${param.index}</div>
                    <div class="param-name" title="${param.name}">${param.name}</div>
                    <div class="param-current" id="current-${param.id}">${param.currentValue}</div>
                    <div class="param-setting">
                        <div class="toggle-switch ${param.settingValue ? 'active' : ''}"
                             id="toggle-${param.id}"
                             onclick="protectionManager.toggleParam('${param.id}')">
                        </div>
                    </div>
                    <div class="param-value" id="value-${param.id}">${param.settingValue}</div>
                `;
                return row;
            }

            /**
             * 切换参数设定值
             */
            toggleParam(paramId) {
                const param = this.protectionParams.find(p => p.id === paramId);
                if (!param) return;

                // 切换设定值
                param.settingValue = param.settingValue ? 0 : 1;

                // 更新界面
                const toggle = document.getElementById(`toggle-${paramId}`);
                const valueDisplay = document.getElementById(`value-${paramId}`);

                if (param.settingValue) {
                    toggle.classList.add('active');
                } else {
                    toggle.classList.remove('active');
                }

                valueDisplay.textContent = param.settingValue;

                // 记录修改
                this.modifiedValues[paramId] = param.settingValue;
                this.updateModifiedCount();

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

            /**
             * 连接MQTT服务器
             */
            connectMQTT() {
                if (this.isConnected) {
                    console.log('MQTT已连接');
                    alert('MQTT已连接');
                    return;
                }

                console.log('正在连接MQTT服务器...');

                // 尝试多种连接方式
                const connectionUrls = [
                    `ws://localhost:${this.mqttConfig.port}/mqtt`,
                    `ws://127.0.0.1:${this.mqttConfig.port}/mqtt`,
                    'ws://mqtt.qizhiyun.cc/mqtt',
                    'wss://mqtt.qizhiyun.cc/mqtt'
                ];

                this.tryConnectMQTT(connectionUrls, 0);
            }

            /**
             * 尝试连接MQTT服务器
             */
            tryConnectMQTT(urls, index) {
                if (index >= urls.length) {
                    console.error('所有MQTT连接尝试都失败了');
                    alert('MQTT连接失败，将使用模拟模式');
                    this.enableSimulationMode();
                    return;
                }

                const url = urls[index];
                console.log(`尝试连接: ${url}`);

                const options = {
                    clientId: this.mqttConfig.clientId,
                    username: this.mqttConfig.username,
                    password: this.mqttConfig.password,
                    cleanSession: true,
                    keepAlive: 30,
                    connectTimeout: 10000,
                };

                try {
                    this.mqttClient = mqtt.connect(url, options);

                    const connectTimeout = setTimeout(() => {
                        if (!this.isConnected) {
                            console.log(`连接超时: ${url}`);
                            this.mqttClient.end();
                            this.tryConnectMQTT(urls, index + 1);
                        }
                    }, 10000);

                    this.mqttClient.on('connect', () => {
                        clearTimeout(connectTimeout);
                        this.isConnected = true;
                        console.log(`MQTT连接成功: ${url}`);
                        alert(`MQTT连接成功: ${url}`);
                        this.updateStatusDisplay();

                        // 订阅数据主题
                        this.mqttClient.subscribe(this.mqttConfig.subscribeTopic, { qos: 1 }, (err) => {
                            if (err) {
                                console.error('订阅失败:', err);
                            } else {
                                console.log('成功订阅主题:', this.mqttConfig.subscribeTopic);
                            }
                        });
                    });

                    this.mqttClient.on('message', (topic, message) => {
                        this.handleMQTTMessage(topic, message);
                    });

                    this.mqttClient.on('error', (error) => {
                        clearTimeout(connectTimeout);
                        console.error(`MQTT连接错误 (${url}):`, error);
                        this.isConnected = false;
                        this.updateStatusDisplay();
                        this.tryConnectMQTT(urls, index + 1);
                    });

                    this.mqttClient.on('close', () => {
                        console.log('MQTT连接已断开');
                        this.isConnected = false;
                        this.updateStatusDisplay();
                    });

                } catch (error) {
                    console.error(`MQTT连接失败 (${url}):`, error);
                    this.tryConnectMQTT(urls, index + 1);
                }
            }

            /**
             * 启用模拟模式
             */
            enableSimulationMode() {
                console.log('启用模拟模式');
                this.isConnected = true; // 模拟连接状态
                this.updateStatusDisplay();

                // 模拟接收数据
                this.startSimulation();
            }

            /**
             * 开始数据模拟
             */
            startSimulation() {
                setInterval(() => {
                    const simulatedData = {
                        id: Date.now().toString(),
                        version: "1.0",
                        properties: {}
                    };

                    // 随机更新一些参数
                    const randomParams = Math.floor(Math.random() * 10) + 1;
                    for (let i = 0; i < randomParams; i++) {
                        const paramIndex = Math.floor(Math.random() * 64) + 1;
                        const paramId = `PROT_ENABLE_${paramIndex}`;
                        simulatedData.properties[paramId] = {
                            value: Math.random() > 0.5 ? 1 : 0,
                            time: Date.now()
                        };
                    }

                    this.handleMQTTMessage('simulation', JSON.stringify(simulatedData));
                }, 5000); // 每5秒模拟一次数据
            }

            /**
             * 处理MQTT消息
             */
            handleMQTTMessage(topic, message) {
                try {
                    const data = JSON.parse(message.toString());
                    console.log('收到MQTT消息:', data);

                    this.dataCount++;
                    this.updateCurrentValues(data);
                    this.updateStatusDisplay();

                } catch (error) {
                    console.error('消息处理错误:', error);
                }
            }

            /**
             * 更新当前值显示
             */
            updateCurrentValues(data) {
                if (data.properties) {
                    Object.keys(data.properties).forEach(propId => {
                        const prop = data.properties[propId];
                        const param = this.protectionParams.find(p => p.id === propId);

                        if (param) {
                            param.currentValue = parseInt(prop.value) || 0;
                            this.currentValues[propId] = param.currentValue;

                            // 更新界面显示
                            const currentDisplay = document.getElementById(`current-${propId}`);
                            if (currentDisplay) {
                                currentDisplay.textContent = param.currentValue;
                            }
                        }
                    });
                }
            }

            /**
             * 下载配置到设备
             */
            downloadConfig() {
                if (!this.isConnected) {
                    alert('请先连接MQTT服务器');
                    return;
                }

                if (Object.keys(this.modifiedValues).length === 0) {
                    alert('没有修改的参数需要下载');
                    return;
                }

                // 构建MQTT消息
                const message = {
                    id: Date.now().toString(),
                    version: "1.0",
                    params: {}
                };

                // 添加修改的参数
                Object.keys(this.modifiedValues).forEach(paramId => {
                    message.params[paramId] = {
                        value: this.modifiedValues[paramId],
                        time: Date.now()
                    };
                });

                // 发送配置消息
                const messageStr = JSON.stringify(message);
                console.log('发送配置消息:', messageStr);

                this.mqttClient.publish(this.mqttConfig.publishTopic, messageStr, { qos: 1 }, (err) => {
                    if (err) {
                        console.error('配置下载失败:', err);
                        alert('配置下载失败: ' + err.message);
                    } else {
                        console.log('配置下载成功');
                        alert(`配置下载成功！已发送 ${Object.keys(this.modifiedValues).length} 个参数的修改`);

                        // 清空修改记录
                        this.modifiedValues = {};
                        this.updateModifiedCount();
                    }
                });
            }

            /**
             * 更新修改计数
             */
            updateModifiedCount() {
                this.modifiedCount = Object.keys(this.modifiedValues).length;
                document.getElementById('modified-count').textContent = this.modifiedCount;
            }

            /**
             * 更新状态显示
             */
            updateStatusDisplay() {
                const statusIndicator = document.getElementById('mqtt-status');
                const connectionText = document.getElementById('connection-text');
                const lastUpdate = document.getElementById('last-update');
                const dataCount = document.getElementById('data-count');

                if (this.isConnected) {
                    statusIndicator.classList.add('connected');
                    connectionText.textContent = '已连接';
                } else {
                    statusIndicator.classList.remove('connected');
                    connectionText.textContent = '未连接';
                }

                lastUpdate.textContent = new Date().toLocaleTimeString();
                dataCount.textContent = this.dataCount;
            }
        }

        // 全局变量和函数
        let protectionManager;

        /**
         * 页面初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            console.log('保护使能配置页面初始化...');
            protectionManager = new ProtectionEnableManager();
            console.log('保护使能配置页面初始化完成');
        });

        /**
         * 连接MQTT（全局函数）
         */
        function connectMQTT() {
            protectionManager.connectMQTT();
        }

        /**
         * 下载配置（全局函数）
         */
        function downloadConfig() {
            protectionManager.downloadConfig();
        }

        // 监听来自父窗口的消息（用于测试）
        window.addEventListener('message', function(event) {
            if (event.data.type === 'mqtt-data') {
                console.log('收到测试数据:', event.data.data);
                if (protectionManager) {
                    protectionManager.handleMQTTMessage('test', JSON.stringify(event.data.data));
                }
            }
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (protectionManager && protectionManager.mqttClient) {
                protectionManager.mqttClient.end();
            }
        });
    </script>
</body>
</html>
