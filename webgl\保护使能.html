<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保护使能 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow: auto;
            position: relative;
        }

        /* 科技感背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.08) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 15px;
        }

        .header {
            text-align: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 2px solid rgba(0, 212, 255, 0.4);
        }

        .header h1 {
            font-size: 28px;
            color: #00d4ff;
            text-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
            font-weight: bold;
        }

        .main-content {
            flex: 1;
            display: flex;
            gap: 15px;
            position: relative;
            padding-bottom: 70px; /* 为底部按钮预留空间 */
        }

        .protection-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 15px;
            overflow: visible;
            min-width: 0; /* 防止内容溢出影响平分 */
        }

        .panel-title {
            font-size: 18px;
            color: #00d4ff;
            text-align: center;
            margin-bottom: 15px;
            padding: 8px 0;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            font-weight: bold;
        }

        .params-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 18px; /* 适度缩小字体 */
    }

        .params-table th {
        background: rgba(0, 212, 255, 0.3);
        color: #00d4ff;
        padding: 8px 6px; /* 适度缩小内边距 */
        text-align: center;
        border: 1px solid rgba(0, 212, 255, 0.4);
        font-weight: bold;
        font-size: 16px; /* 适度缩小表头字体 */
    }

        .params-table td {
        padding: 6px 8px; /* 适度缩小内边距 */
        text-align: center;
        border: 1px solid rgba(0, 212, 255, 0.2);
        background: rgba(42, 49, 66, 0.7);
        vertical-align: middle;
        height: 46px; /* 适度缩小行高 */
    }

        .params-table tr:hover td {
            background: rgba(42, 49, 66, 0.9);
            border-color: rgba(0, 212, 255, 0.4);
        }

        .param-index {
        font-size: 16px; /* 适度缩小序号字体 */
        color: #7a8ba0;
        font-weight: bold;
        width: 42px; /* 适度缩小宽度 */
        text-align: right;
        padding-right: 8px;
    }

        .param-name {
        font-size: 15px; /* 适度缩小参数名称字体 */
        color: #ffffff;
        text-align: left;
        padding-left: 6px;
        line-height: 1.3; /* 适度缩小行高 */
        max-width: 200px;
        word-wrap: break-word;
        overflow: visible;
        white-space: normal;
    }

        .param-current {
        font-size: 16px; /* 适度缩小当前值字体 */
        color: #00d4ff;
        font-weight: bold;
        width: 55px; /* 适度缩小宽度 */
    }

        .param-setting {
            width: 60px; /* 增大设置列宽度 */
        }

        .toggle-switch {
            position: relative;
            width: 60px; /* 增大开关宽度 */
            height: 30px; /* 增大开关高度 */
            background: #dc3545;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 212, 255, 0.3);
            margin: 0 auto;
        }

        .toggle-switch.active {
            background: #007bff;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px; /* 增大滑块 */
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .toggle-switch.active::before {
            transform: translateX(30px); /* 调整滑块位置 */
        }
        .send-button {
            position: fixed; /* 改为fixed确保始终可见 */
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .send-button:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.6);
            transform: translateX(-50%) translateY(-2px);
        }

        .send-button:active {
            transform: translateX(-50%) translateY(0);
            box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
        }

        .send-button:disabled {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            cursor: not-allowed;
            box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
        }

        .send-button::before {
            content: "📤";
            font-size: 18px;
        }

        /* 状态提示样式 */
        .status-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 20px 30px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            z-index: 2000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            display: none;
        }

        .status-message.success {
            border-color: rgba(40, 167, 69, 0.6);
            color: #28a745;
        }

        .status-message.error {
            border-color: rgba(220, 53, 69, 0.6);
            color: #dc3545;
        }

        .status-message.warning {
            border-color: rgba(255, 193, 7, 0.6);
            color: #ffc107;
        }

        /* 参数修改高亮样式 */
        .modified-param {
            background-color: rgba(255, 193, 7, 0.3) !important;
            transition: background-color 0.3s ease;
        }

        .params-table tr:hover .modified-param {
            background-color: rgba(255, 193, 7, 0.4) !important;
        }

        /* MQTT 连接状态指示器样式 */
        .mqtt-status-container {
            position: fixed;
            top: 15px;
            right: 15px;
            z-index: 1000;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .mqtt-connection-status {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            display: inline-block;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .mqtt-connection-status.connected {
            background-color: #4CAF50;
            border-color: #45a049;
        }

        .mqtt-connection-status.disconnected {
            background-color: #f44336;
            border-color: #da190b;
        }

        .data-timestamp {
            font-size: 11px;
            color: #7a8ba0;
            padding: 4px 8px;
            background: rgba(26, 31, 46, 0.8);
            border-radius: 4px;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

    </style>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>保护使能</h1>
        </div>

        <div class="main-content">
            <div class="protection-panel">
                <div class="panel-title">保护功能使能1</div>
                <table class="params-table">
                    <thead>
                        <tr>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>设定值</th>
                        <th>当前值</th>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>设定值</th>
                        <th>当前值</th>
                    </tr>
                    </thead>
                    <tbody id="protection-table-1">
                        <!-- 保护使能参数1-32将在这里动态生成 -->
                    </tbody>
                </table>
            </div>

            <div class="protection-panel">
                <div class="panel-title">保护功能使能2</div>
                <table class="params-table">
                    <thead>
                        <tr>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>设定值</th>
                        <th>当前值</th>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>设定值</th>
                        <th>当前值</th>
                    </tr>
                    </thead>
                    <tbody id="protection-table-2">
                        <!-- 保护使能参数33-64将在这里动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
        <button class="send-button" id="send-settings-btn" onclick="sendParameterSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <script>
        /**
         * 保护使能配置管理类
         * 负责数据处理和界面更新
         */
        class ProtectionEnableManager {
            constructor() {
            this.protectionParams = [];
            this.currentValues = {};
            this.modifiedValues = {};

            this.initializeParams();
            this.createParamTables();
        }



            /**
             * 初始化保护使能参数定义
             * 使用指定的64个MQTT标识符ID直接匹配
             */
            initializeParams() {
                // 保护功能使能1组 (32个参数)
                const group1 = [
                    { mqttId: '2101_GOA1', name: '电网线电压有效值Ⅰ段过压报警使能' },
                    { mqttId: '2101_GOP2', name: '电网线电压有效值Ⅱ段过压保护使能' },
                    { mqttId: '2101_GOP3', name: '电网线电压有效值Ⅲ段过压保护使能' },
                    { mqttId: '2101_GIOP', name: '电网线电压瞬时值过压保护使能' },
                    { mqttId: '2101_GUA1', name: '电网线电压有效值Ⅰ段欠压报警使能' },
                    { mqttId: '2101_GUP2', name: '电网线电压有效值Ⅱ段欠压保护使能' },
                    { mqttId: '2101_GUP3', name: '电网线电压有效值Ⅲ段欠压保护使能' },
                    { mqttId: '2101_GVIP', name: '电网线电压有效值不平衡保护使能' },
                    { mqttId: '2101_GVPLP', name: '电网线电压缺相保护使能' },
                    { mqttId: '2101_GVRSP', name: '电网电压反序保护使能' },
                    { mqttId: '2101_SSLP', name: '同步信号丢失保护使能' },
                    { mqttId: '2101_SHSFP', name: 'SVG侧霍尔传感器故障保护使能' },
                    { mqttId: '2101_ABSSCFP', name: '模拟板采样通道自检故障保护使能' },
                    { mqttId: '2101_ZSVOP', name: '零序电压超标保护使能' },
                    { mqttId: '2101_SOCOA1', name: 'SVG输出电流有效值Ⅰ段过流报警使能' },
                    { mqttId: '2101_SOCOP2', name: 'SVG输出电流有效值Ⅱ段过流保护使能' },
                    { mqttId: '2101_SOCIOP', name: 'SVG输出电流瞬时值过流保护使能' },
                    { mqttId: '2101_SOCHOP', name: 'SVG输出电流硬件过流保护使能' },
                    { mqttId: '2101_SOCPLP', name: 'SVG输出电流缺相保护使能' },
                    { mqttId: '2101_SOCCLA', name: 'SVG输出电流指令限幅报警使能' },
                    { mqttId: '2101_SICOCDFP', name: 'SVG瞬时电流过流(CT检测方式)故障保护使能' },
                    { mqttId: '2101_PTFP', name: 'PT故障保护使能' },
                    { mqttId: '2101_SZSCFP', name: 'SVG零序电流故障保护使能' },
                    { mqttId: '2101_PUGF', name: '功率单元状态一般性故障使能' },
                    { mqttId: '2101_PUUOP', name: '功率单元UDC过压保护使能' },
                    { mqttId: '2101_PUUIP', name: '功率单元UDC不平衡保护使能' },
                    { mqttId: '2101_PUHP', name: '功率单元硬件保护(HW)使能' },
                    { mqttId: '2101_PUSCFP', name: '功率单元自检故障保护使能' },
                    { mqttId: '2101_PUSIP', name: '功率单元状态不一致保护使能' },
                    { mqttId: '2101_RS485CTF', name: 'RS485通信超时故障使能' },
                    { mqttId: '2101_RS485CCF', name: 'RS485通信校验故障使能' },
                    { mqttId: '2101_DRTF', name: 'DRAM读超时故障使能' }
                ];

                // 保护功能使能2组 (32个参数)
                const group2 = [
                    { mqttId: '2102_DF', name: 'DRAM故障使能' },
                    { mqttId: '2102_DWPF', name: 'DRAM写参数故障使能' },
                    { mqttId: '2102_WWRP', name: 'WDI看门狗复位保护使能' },
                    { mqttId: '2102_CTF', name: '充电超时故障使能' },
                    { mqttId: '2102_TSNCSDF', name: '行程开关未合故障使能' },
                    { mqttId: '2102_CCK1NE', name: '充电接触器K1不吸合使能' },
                    { mqttId: '2102_CCK1ND', name: '充电接触器K1不分开使能' },
                    { mqttId: '2102_CBQF1NE', name: '断路器QF1不吸合使能' },
                    { mqttId: '2102_CBQF1ND', name: '断路器QF1不分开使能' },
                    { mqttId: '2102_CCST', name: 'CAN通信发送超时使能' },
                    { mqttId: '2102_CCRT', name: 'CAN通信接收超时使能' },
                    { mqttId: '2102_WCSPF', name: '水冷系统电源故障使能' },
                    { mqttId: '2102_RFPE', name: '读铁电参数错误使能' },
                    { mqttId: '2102_TOA', name: '变压器超温报警使能' },
                    { mqttId: '2102_TOT', name: '变压器超温跳闸使能' },
                    { mqttId: '2102_TLGA', name: '变压器轻瓦斯报警使能' },
                    { mqttId: '2102_THGT', name: '变压器重瓦斯跳闸使能' },
                    { mqttId: '2102_TPA', name: '变压器压力报警使能' },
                    { mqttId: '2102_TPT', name: '变压器压力跳闸使能' },
                    { mqttId: '2102_FPLP', name: '风机缺相保护使能' },
                    { mqttId: '2102_USCF', name: '单元短路故障使能' },
                    { mqttId: '2102_IOC2T', name: '瞬时过流2跳闸使能' },
                    { mqttId: '2102_GLV1P', name: '电网低电压1保护使能' },
                    { mqttId: '2102_GLV2P', name: '电网低电压2保护使能' },
                    { mqttId: '2102_ARFCLP', name: '自动恢复失败次数超限保护使能' },
                    { mqttId: '2102_FRTP', name: '故障复位超时保护使能' },
                    { mqttId: '2102_ICFBP', name: '柜间光纤断保护使能' },
                    { mqttId: '2102_WCSOSA', name: '水冷系统运行状态异常使能' },
                    { mqttId: '2102_WCSCF', name: '水冷系统综合故障使能' },
                    { mqttId: '2102_WCSA', name: '水冷系统报警使能' },
                    { mqttId: '2102_WCSRS', name: '水冷系统请求停止使能' },
                    { mqttId: '2102_HSFCF', name: '高速光纤通信故障使能' }
                ];

                // 创建第一组参数（1-32）
                group1.forEach((param, index) => {
                    this.protectionParams.push({
                        id: `param_${index + 1}`,
                        index: index + 1,
                        mqttId: param.mqttId,
                        name: param.name,
                        currentValue: 0,
                        settingValue: 0,
                        group: 1,
                        isInitialized: false // 首次同步标记
                    });
                });

                // 创建第二组参数（33-64）
                group2.forEach((param, index) => {
                    this.protectionParams.push({
                        id: `param_${index + 33}`,
                        index: index + 1,
                        mqttId: param.mqttId,
                        name: param.name,
                        currentValue: 0,
                        settingValue: 0,
                        group: 2,
                        isInitialized: false // 首次同步标记
                    });
                });
            }

            /**
             * 创建参数表格界面
             */
            createParamTables() {
                const table1 = document.getElementById('protection-table-1');
                const table2 = document.getElementById('protection-table-2');

                // 创建第一组参数（1-32），每行显示两个参数
                for (let i = 0; i < 16; i++) {
                    const param1 = this.protectionParams[i * 2];
                    const param2 = this.protectionParams[i * 2 + 1];
                    const row = this.createParamTableRow(param1, param2);
                    table1.appendChild(row);
                }

                // 创建第二组参数（33-64），每行显示两个参数
                for (let i = 0; i < 16; i++) {
                    const param1 = this.protectionParams[32 + i * 2];
                    const param2 = this.protectionParams[32 + i * 2 + 1];
                    const row = this.createParamTableRow(param1, param2);
                    table2.appendChild(row);
                }

                // 初始化所有参数的高亮状态
                this.initializeAllHighlights();
            }

            /**
             * 创建单个参数表格行（包含两个参数）
             */
            createParamTableRow(param1, param2) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="param-index" data-param-id="${param1.id}" data-cell-type="index">${param1.index}</td>
                    <td class="param-name" data-param-id="${param1.id}" data-cell-type="name">${param1.name}</td>
                    <td class="param-setting" data-param-id="${param1.id}" data-cell-type="setting">
                        <div class="toggle-switch ${param1.settingValue ? 'active' : ''}"
                             id="toggle-${param1.id}"
                             onclick="protectionManager.toggleParam('${param1.id}')">
                        </div>
                    </td>
                    <td class="param-current" data-param-id="${param1.id}" data-cell-type="current" id="current-${param1.id}">${param1.currentValue}</td>
                    <td class="param-index" data-param-id="${param2.id}" data-cell-type="index">${param2.index}</td>
                    <td class="param-name" data-param-id="${param2.id}" data-cell-type="name">${param2.name}</td>
                    <td class="param-setting" data-param-id="${param2.id}" data-cell-type="setting">
                        <div class="toggle-switch ${param2.settingValue ? 'active' : ''}"
                             id="toggle-${param2.id}"
                             onclick="protectionManager.toggleParam('${param2.id}')">
                        </div>
                    </td>
                    <td class="param-current" data-param-id="${param2.id}" data-cell-type="current" id="current-${param2.id}">${param2.currentValue}</td>
                `;
                return row;
            }

            /**
             * 切换参数设定值
             */
            toggleParam(paramId) {
                const param = this.protectionParams.find(p => p.id === paramId);
                if (!param) return;

                // 切换设定值
                param.settingValue = param.settingValue ? 0 : 1;

                // 更新界面
                const toggle = document.getElementById(`toggle-${paramId}`);
                if (param.settingValue) {
                    toggle.classList.add('active');
                } else {
                    toggle.classList.remove('active');
                }

                // 更新高亮状态
                this.updateHighlightStatus(paramId);

                // 记录修改
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

            /**
             * 更新参数高亮状态
             * 当设定值与当前值不一致时高亮显示相关单元格
             */
            updateHighlightStatus(paramId) {
                const param = this.protectionParams.find(p => p.id === paramId);
                if (!param) return;

                // 检查是否需要高亮
                const needsHighlight = param.settingValue !== param.currentValue;

                // 获取所有需要高亮的单元格
                const cells = document.querySelectorAll(`[data-param-id="${paramId}"]`);
                
                cells.forEach(cell => {
                    if (needsHighlight) {
                        cell.classList.add('modified-param');
                    } else {
                        cell.classList.remove('modified-param');
                    }
                });
            }

            /**
             * 初始化所有参数的高亮状态
             * 在页面加载和表格创建后调用
             */
            initializeAllHighlights() {
                this.protectionParams.forEach(param => {
                    this.updateHighlightStatus(param.id);
                });
            }

            /**
             * 更新参数当前值（从 MQTT 数据）
             * 使用 MQTT 标识符 ID 直接匹配参数
             * @param {string} mqttId - MQTT 参数标识符
             * @param {number} value - 参数值
             */
            updateCurrentValueFromMQTT(mqttId, value) {
                // console.log(`[调试] 尝试更新参数: ${mqttId} = ${value}`);

                // 直接通过 MQTT ID 查找对应的参数
                const param = this.protectionParams.find(p => p.mqttId === mqttId);

                if (param) {
                    // console.log(`[调试] 找到参数: ${param.name} (ID: ${param.id}, 索引: ${param.index})`);

                    // 更新参数当前值
                    const oldCurrentValue = param.currentValue;
                    param.currentValue = value;

                    // 首次数据同步：如果参数未初始化，将设定值设为当前值
                    if (!param.isInitialized) {
                        const oldSettingValue = param.settingValue;
                        param.settingValue = value;
                        param.isInitialized = true;
                        
                        // 更新界面上的设定值显示（开关状态）
                        const toggleElement = document.getElementById(`toggle-${param.id}`);
                        if (toggleElement) {
                            if (value) {
                                toggleElement.classList.add('active');
                            } else {
                                toggleElement.classList.remove('active');
                            }
                        }
                        
                        // 记录修改
                        this.modifiedValues[param.id] = param.settingValue;
                        
                        console.log(`首次同步参数 ${param.name}: 当前值=${value}, 设定值=${value} (已初始化)`);
                    }

                    // 更新界面显示（当前值）
                    const currentElement = document.getElementById(`current-${param.id}`);
                    if (currentElement) {
                        // console.log(`[调试] 更新界面元素 current-${param.id}: ${oldCurrentValue} -> ${value}`);
                        currentElement.textContent = value;
                        currentElement.style.color = '#00d4ff'; // 恢复正常颜色
                    } else {
                        // console.warn(`[调试] 未找到界面元素: current-${param.id}`);
                    }

                    // 更新高亮状态
                    this.updateHighlightStatus(param.id);

                    // console.log(`MQTT 更新参数 ${param.name} (${mqttId}): 当前值 ${oldCurrentValue} -> ${value}, 设定值保持: ${param.settingValue}`);
                } else {
                    // console.warn(`[调试] 未找到对应的参数对象: ${mqttId}`);
                    // console.log(`[调试] 当前所有参数的 MQTT ID:`, this.protectionParams.map(p => p.mqttId));
                }
            }

            /**
             * 批量更新 MQTT 数据（旧格式 - 嵌套对象）
             * 使用 MQTT 标识符 ID 直接匹配参数
             * @param {Object} mqttData - MQTT 数据对象
             */
            updateFromMQTTData(mqttData) {
                if (!mqttData || !mqttData.properties) {
                    console.warn('MQTT 数据格式无效');
                    return;
                }

                let updatedCount = 0;
                let unmatchedCount = 0;
                
                Object.keys(mqttData.properties).forEach(propertyId => {
                    const propertyData = mqttData.properties[propertyId];
                    const value = propertyData.value;

                    // 直接使用 MQTT ID 查找参数
                    const param = this.protectionParams.find(p => p.mqttId === propertyId);
                    if (param) {
                        this.updateCurrentValueFromMQTT(propertyId, value);
                        updatedCount++;
                    } else {
                        unmatchedCount++;
                        // console.log(`未匹配的 MQTT 参数: ${propertyId}`);
                    }
                });

                console.log(`MQTT 批量更新完成，共更新 ${updatedCount} 个保护使能参数，未匹配 ${unmatchedCount} 个`);

                // 更新数据时间戳
                updateDataTimestamp(new Date());
            }

            /**
             * 批量更新 MQTT 数据（新格式 - JSON 数组）
             * 使用 MQTT 标识符 ID 直接匹配参数
             * @param {Array} mqttArray - MQTT 数据数组
             */
            updateFromJSONArray(mqttArray) {
                if (!Array.isArray(mqttArray) || mqttArray.length === 0) {
                    console.warn('MQTT 数据格式无效：应为非空数组');
                    return;
                }

                let updatedCount = 0;
                let invalidCount = 0;
                let unmatchedCount = 0;

                mqttArray.forEach(item => {
                    if (!item || typeof item !== 'object') {
                        invalidCount++;
                        return;
                    }

                    // 支持两种格式：
                    // 1. {id, value} 格式
                    // 2. {id, name, ts, value} 格式（用户提供的格式）
                    let id = item.id;
                    let value = item.value;

                    if (!id || value === undefined || value === null) {
                        invalidCount++;
                        console.warn('MQTT 数据项格式无效：缺少 id 或 value 字段', item);
                        return;
                    }

                    // 处理字符串类型的值
                    let numericValue;
                    if (typeof value === 'string') {
                        numericValue = parseInt(value, 10);
                    } else if (typeof value === 'number') {
                        numericValue = value;
                    } else {
                        invalidCount++;
                        console.warn(`MQTT 数据值类型无效: ${typeof value} (id: ${id})`);
                        return;
                    }

                    if (isNaN(numericValue)) {
                        invalidCount++;
                        console.warn(`MQTT 数据值转换失败: ${value} (id: ${id})`);
                        return;
                    }

                    // 直接使用 MQTT ID 查找参数
                    const param = this.protectionParams.find(p => p.mqttId === id);
                    if (param) {
                        this.updateCurrentValueFromMQTT(id, numericValue);
                        updatedCount++;
                    } else {
                        unmatchedCount++;
                        // console.log(`未匹配的 MQTT 参数: ${id}`);
                    }
                });

                console.log(`MQTT 批量更新完成：更新 ${updatedCount} 个参数，无效 ${invalidCount} 个，未匹配 ${unmatchedCount} 个`);

                // 更新数据时间戳
                updateDataTimestamp(new Date());
            }

            /**
             * 获取所有配置数据
             */
            getConfigurationData() {
                return {
                    timestamp: new Date().toISOString(),
                    parameters: this.protectionParams.map(param => ({
                        id: param.id,
                        index: param.index,
                        name: param.name,
                        currentValue: param.currentValue,
                        settingValue: param.settingValue,
                        group: param.group,
                        isInitialized: param.isInitialized,
                        mqttId: param.mqttId
                    })),
                    modifiedValues: this.modifiedValues
                };
            }

            /**
             * 获取需要发送的参数设置（设定值与当前值不一致的参数）
             * @returns {Array} 需要发送的参数数组
             */
            getModifiedParameters() {
                const modifiedParams = [];

                this.protectionParams.forEach(param => {
                    if (param.settingValue !== param.currentValue) {
                        modifiedParams.push({
                            id: param.mqttId,
                            value: param.settingValue.toString(), // 转换为字符串格式
                            name: param.name, // 用于日志显示
                            currentValue: param.currentValue,
                            settingValue: param.settingValue
                        });
                    }
                });

                return modifiedParams;
            }

            /**
             * 获取用于 MQTT 发送的参数数组（仅包含 id 和 value）
             * @returns {Array} MQTT 发送格式的参数数组
             */
            getMQTTParameterArray() {
                const modifiedParams = this.getModifiedParameters();
                return modifiedParams.map(param => ({
                    id: param.id,
                    value: param.value
                }));
            }

            /**
             * 从保存的配置数据中恢复参数状态
             * @param {Object} savedData - 保存的配置数据
             */
            restoreFromConfigurationData(savedData) {
                if (!savedData || !savedData.parameters) {
                    console.warn('无效的配置数据格式');
                    return;
                }

                savedData.parameters.forEach(savedParam => {
                    const param = this.protectionParams.find(p => p.id === savedParam.id);
                    if (param) {
                        // 恢复设定值和初始化状态
                        param.settingValue = savedParam.settingValue;
                        param.isInitialized = savedParam.isInitialized || false;
                        
                        // 更新界面显示
                        const toggleElement = document.getElementById(`toggle-${param.id}`);
                        if (toggleElement) {
                            if (param.settingValue) {
                                toggleElement.classList.add('active');
                            } else {
                                toggleElement.classList.remove('active');
                            }
                        }
                    }
                });

                // 恢复修改记录
                if (savedData.modifiedValues) {
                    Object.assign(this.modifiedValues, savedData.modifiedValues);
                }

                console.log(`已从保存的配置数据中恢复 ${savedData.parameters.length} 个参数状态`);
            }
        }

        // 全局变量
        let protectionManager;
        let mqttClient = null;
        let isConnected = false;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        const reconnectInterval = 5000;

        /**
         * MQTT 保护使能数据管理器
         * 负责连接 MQTT 服务器并处理保护使能相关数据
         */
        class MQTTProtectionManager {
            constructor() {
                this.mqttClient = null;
                this.isConnected = false;
                this.subscriptionTopic = '/197/D19EOEN59V1MJ/ws/service';
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                this.reconnectInterval = 5000;
                this.lastDataUpdate = null;
                this.messageCount = 0;

                this.init();
            }

            /**
             * 初始化 MQTT 连接
             */
            async init() {
                console.log('初始化 MQTT 保护使能数据管理器...');
                try {
                    await this.connectMQTT();
                    await this.subscribeToTopic();
                    this.setupMessageHandler();
                    this.setupReconnectHandler();
                    console.log('MQTT 保护使能数据管理器初始化完成');
                } catch (error) {
                    console.error('MQTT 保护使能数据管理器初始化失败:', error);
                    this.scheduleReconnect();
                }
            }

            /**
             * 连接 MQTT 服务器
             */
            async connectMQTT() {
                return new Promise((resolve, reject) => {
                    try {
                        // 使用与电气系统相同的 MQTT 配置
                        const options = {
                            username: 'FastBee',
                            password: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjdhM2RjZWY1LTE5ODgtNDg4OS04OTAzLTIwY2I0YjIyZDA0YSJ9.g1HCISIvQd6YkNgWhblKnXqHeRI74lnP1F8qZOd9XV5a7J41Qi77f9jLxxWd_EVN0XJPP1haYeRK3Uz_xrbEuA',
                            cleanSession: true,
                            keepAlive: 30,
                            clientId: 'web-' + Math.random().toString(16).substr(2),
                            connectTimeout: 60000,
                        };

                        // 使用已验证的 MQTT 服务器地址
                        const url = 'wss://mqtt.qizhiyun.cc/mqtt';
                        console.log('连接到 MQTT 服务器（保护使能）：', url);

                        // 使用全局 mqtt 对象连接
                        if (typeof mqtt !== 'undefined') {
                            this.mqttClient = mqtt.connect(url, options);
                        } else {
                            throw new Error('MQTT 客户端库未加载');
                        }

                        this.mqttClient.on('connect', () => {
                            console.log('MQTT 保护使能连接成功');
                            this.isConnected = true;
                            this.reconnectAttempts = 0;
                            updateMQTTStatus('connected', 'MQTT 已连接');
                            resolve();
                        });

                        this.mqttClient.on('error', (error) => {
                            console.error('MQTT 保护使能连接失败:', error);
                            this.isConnected = false;
                            updateMQTTStatus('disconnected', `MQTT 连接失败: ${error.message}`);
                            reject(error);
                        });

                        this.mqttClient.on('close', () => {
                            console.log('MQTT 保护使能连接已断开');
                            this.isConnected = false;
                            updateMQTTStatus('disconnected', 'MQTT 连接已断开');
                        });

                    } catch (error) {
                        console.error('MQTT 连接配置错误:', error);
                        reject(error);
                    }
                });
            }

            /**
             * 订阅保护使能数据主题
             */
            async subscribeToTopic() {
                return new Promise((resolve, reject) => {
                    if (!this.mqttClient || !this.isConnected) {
                        reject(new Error('MQTT 客户端未连接'));
                        return;
                    }

                    this.mqttClient.subscribe(this.subscriptionTopic, { qos: 1 }, (err, granted) => {
                        if (err) {
                            console.error('订阅保护使能主题失败:', err);
                            reject(err);
                        } else {
                            console.log('成功订阅保护使能主题:', this.subscriptionTopic);
                            resolve(granted);
                        }
                    });
                });
            }

            /**
             * 设置消息处理器
             */
            setupMessageHandler() {
                if (!this.mqttClient) return;

                this.mqttClient.on('message', (topic, message) => {
                    try {
                        if (topic === this.subscriptionTopic) {
                            this.messageCount++;
                            const data = JSON.parse(message.toString());
                            console.log(`收到保护使能数据 (#${this.messageCount}):`, data);
                            
                            // 记录原始数据用于调试
                            console.log('原始消息长度:', message.length, '字节');
                            
                            this.processProtectionData(data);
                        }
                    } catch (error) {
                        console.error('处理 MQTT 消息失败:', error);
                        console.error('原始消息:', message.toString());
                    }
                });
            }

            /**
             * 设置重连处理器
             */
            setupReconnectHandler() {
                if (!this.mqttClient) return;

                this.mqttClient.on('reconnect', () => {
                    console.log('正在重连 MQTT 保护使能...');
                    this.reconnectAttempts++;
                    updateMQTTStatus('disconnected', `重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                });

                this.mqttClient.on('offline', () => {
                    console.log('MQTT 保护使能离线');
                    this.isConnected = false;
                    this.scheduleReconnect();
                });
            }

            /**
             * 处理保护使能数据（支持多种数据格式）
             */
            processProtectionData(data) {
                if (protectionManager) {
                    if (data && data.message && Array.isArray(data.message)) {
                        // 处理包含 message 数组的格式（实际接收到的格式）
                        console.log('处理 message 数组格式数据，数组长度:', data.message.length);
                        protectionManager.updateFromJSONArray(data.message);
                    } else {
                        console.warn('未识别的数据格式:', data);
                    }
                    this.lastDataUpdate = new Date();
                } else {
                    console.warn('保护管理器未初始化，无法处理数据');
                }
            }

            /**
             * 计划重连
             */
            scheduleReconnect() {
                if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                    console.error(`MQTT 重连次数超过限制 (${this.maxReconnectAttempts})，停止自动重连`);
                    updateMQTTStatus('disconnected', 'MQTT 连接失败');
                    return;
                }

                const backoffDelay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts), 60000);
                console.log(`第 ${this.reconnectAttempts + 1} 次尝试重连 MQTT 保护使能，${backoffDelay/1000}秒后重试...`);

                setTimeout(() => {
                    this.init();
                }, backoffDelay);
            }

            /**
             * 手动重连
             */
            async reconnect() {
                if (this.mqttClient) {
                    this.mqttClient.end();
                }
                this.reconnectAttempts = 0;
                await this.init();
            }

            /**
             * 断开连接
             */
            disconnect() {
                if (this.mqttClient) {
                    this.mqttClient.end();
                    this.mqttClient = null;
                }
                this.isConnected = false;
                console.log('MQTT 保护使能连接已断开');
            }

            /**
             * 获取连接状态
             */
            getConnectionStatus() {
                return {
                    isConnected: this.isConnected,
                    reconnectAttempts: this.reconnectAttempts,
                    lastDataUpdate: this.lastDataUpdate,
                    subscriptionTopic: this.subscriptionTopic
                };
            }

            /**
             * 发送参数设置到 MQTT 服务器
             * @param {Array} parameterArray - 参数数组，格式：[{id, value}, ...]
             * @returns {Promise} 发送结果
             */
            async sendParameterSettings(parameterArray) {
                return new Promise((resolve, reject) => {
                    if (!this.mqttClient || !this.isConnected) {
                        reject(new Error('MQTT 客户端未连接'));
                        return;
                    }

                    if (!Array.isArray(parameterArray) || parameterArray.length === 0) {
                        reject(new Error('参数数组为空或格式无效'));
                        return;
                    }

                    // 发布主题
                    const publishTopic = '/197/D19EOEN59V1MJ/function/get';

                    // 转换为 JSON 字符串
                    const messagePayload = JSON.stringify(parameterArray);

                    console.log(`发送参数设置到主题: ${publishTopic}`);
                    console.log(`发送数据:`, parameterArray);

                    // 发布消息
                    this.mqttClient.publish(publishTopic, messagePayload, { qos: 1 }, (error) => {
                        if (error) {
                            console.error('MQTT 发送失败:', error);
                            reject(error);
                        } else {
                            console.log('MQTT 参数设置发送成功');
                            resolve({
                                success: true,
                                topic: publishTopic,
                                parameterCount: parameterArray.length,
                                timestamp: new Date()
                            });
                        }
                    });
                });
            }
        }

        // 全局 MQTT 管理器实例
        let mqttProtectionManager = null;

        /**
         * 更新 MQTT 连接状态显示
         */
        function updateMQTTStatus(status, message) {
            const statusElement = document.getElementById('mqtt-status');
            if (statusElement) {
                statusElement.className = `mqtt-connection-status ${status}`;
                statusElement.textContent = message;
            }
        }

        /**
         * 更新数据时间戳
         */
        function updateDataTimestamp(timestamp, customMessage = null) {
            const timestampElement = document.getElementById('data-timestamp');
            if (timestampElement) {
                if (customMessage) {
                    timestampElement.textContent = customMessage;
                } else if (timestamp) {
                    const timeStr = timestamp.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                    timestampElement.textContent = `数据更新: ${timeStr}`;
                } else {
                    timestampElement.textContent = '等待数据...';
                }
            }
        }

        /**
         * 更新发送按钮状态
         */
        function updateSendButtonStatus() {
            const sendButton = document.getElementById('send-settings-btn');
            if (!sendButton) return;

            if (mqttProtectionManager) {
                const status = mqttProtectionManager.getConnectionStatus();
                if (status.isConnected) {
                    sendButton.disabled = false;
                    sendButton.title = '发送参数设置到 MQTT 服务器';
                } else {
                    sendButton.disabled = true;
                    sendButton.title = 'MQTT 未连接，无法发送参数设置';
                }
            } else {
                sendButton.disabled = true;
                sendButton.title = 'MQTT 管理器未初始化';
            }
        }

        /**
         * 初始化 MQTT 连接
         */
        function initMQTTConnection() {
            console.log('初始化 MQTT 保护使能连接...');
            mqttProtectionManager = new MQTTProtectionManager();
        }

        /**
         * 发送参数设置到 MQTT 服务器
         */
        async function sendParameterSettings() {
            if (!protectionManager) {
                showStatusMessage('保护管理器未初始化', 'error');
                return;
            }

            if (!mqttProtectionManager) {
                showStatusMessage('MQTT 管理器未初始化', 'error');
                return;
            }

            // 检查 MQTT 连接状态
            const connectionStatus = mqttProtectionManager.getConnectionStatus();
            if (!connectionStatus.isConnected) {
                showStatusMessage('MQTT 未连接，无法发送参数设置', 'error');
                return;
            }

            // 获取需要发送的参数
            const modifiedParams = protectionManager.getModifiedParameters();

            if (modifiedParams.length === 0) {
                showStatusMessage('没有需要更新的参数（所有参数的设定值与当前值一致）', 'warning');
                return;
            }



            // 禁用发送按钮
            const sendButton = document.getElementById('send-settings-btn');
            if (sendButton) {
                sendButton.disabled = true;
                sendButton.textContent = '发送中...';
            }

            try {
                // 获取 MQTT 格式的参数数组
                const mqttParams = protectionManager.getMQTTParameterArray();

                // 发送参数设置
                const result = await mqttProtectionManager.sendParameterSettings(mqttParams);

                showStatusMessage(
                    `参数设置发送成功！\n发送了 ${result.parameterCount} 个参数\n时间: ${result.timestamp.toLocaleString()}`,
                    'success'
                );

                console.log('参数设置发送成功:', result);

            } catch (error) {
                console.error('发送参数设置失败:', error);
                showStatusMessage(`发送失败: ${error.message}`, 'error');
            } finally {
                // 恢复发送按钮
                if (sendButton) {
                    sendButton.disabled = false;
                    sendButton.textContent = '下载';
                }
            }
        }

        /**
         * 显示状态消息
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型：success, error, warning
         */
        function showStatusMessage(message, type = 'success') {
            const statusElement = document.getElementById('status-message');
            if (!statusElement) return;

            statusElement.textContent = message;
            statusElement.className = `status-message ${type}`;
            statusElement.style.display = 'block';

            // 3秒后自动隐藏
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 3000);
        }

        /**
         * 页面初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            console.log('保护使能配置页面初始化...');

            // 初始化保护管理器
            protectionManager = new ProtectionEnableManager();

            // 初始化 MQTT 连接
            initMQTTConnection();

            // 定期更新连接状态显示和按钮状态
            setInterval(() => {
                if (mqttProtectionManager) {
                    const status = mqttProtectionManager.getConnectionStatus();
                    if (status.isConnected) {
                        updateMQTTStatus('connected', 'MQTT 已连接');
                    } else {
                        updateMQTTStatus('disconnected', `MQTT 未连接 (重试: ${status.reconnectAttempts})`);
                    }
                    // 更新发送按钮状态
                    updateSendButtonStatus();
                }
            }, 1000);

            console.log('保护使能配置页面初始化完成');

            // 显示调试功能说明
            console.log('=== 保护使能 MQTT 调试功能 ===');
            console.log('mqttProtectionManager.getConnectionStatus() - 获取连接状态');
            console.log('mqttProtectionManager.reconnect() - 手动重连');
            console.log('protectionManager.mqttParameterMapping - 查看参数映射表');
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (mqttProtectionManager) {
                console.log('页面卸载，断开 MQTT 连接');
                mqttProtectionManager.disconnect();
            }
        });
    </script>
</body>
</html>
